<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>life.ljs</groupId>
    <artifactId>bank-policy-comparison-platform</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <name>bank-policy-comparison-platform</name>
    <description>bank-policy-comparison-platform</description>
    <packaging>pom</packaging>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.5.3</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>

    <modules>
        <module>common</module>
    </modules>

    <properties>
        <java.version>17</java.version>
        <spring-boot.version>3.5.3</spring-boot.version>
        <spring-cloud.version>2025.0.0</spring-cloud.version>
        <!-- Maven Compiler Plugin Version -->
        <maven.compiler.source>17</maven.compiler.source> <!-- 指定为 JDK 17 -->
        <maven.compiler.target>17</maven.compiler.target> <!-- 指定为 JDK 17 -->
        <!-- Project Encoding -->
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>

        <!-- Lombok for reducing boilerplate code -->
        <lombok.version>1.18.30</lombok.version> <!-- Lombok 通常与各种JDK和Spring Boot版本兼容性较好 -->
        <!-- JUnit 5 -->
        <junit.jupiter.version>5.10.0</junit.jupiter.version> <!-- Spring Boot 3.x 通常使用更新的 JUnit 版本 -->
    </properties>

    <dependencyManagement>
        <dependencies>
            <!-- Spring Cloud Dependency BOM -->
            <!-- 这个dependencyManagement节点非常重要, 它引入了Spring Cloud的依赖版本 -->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope> <!-- 关键: import scope -->
            </dependency>
            <!-- Lombok -->
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
                <scope>provided</scope>
            </dependency>
            <!-- JUnit 5 -->
            <dependency>
                <groupId>org.junit.jupiter</groupId>
                <artifactId>junit-jupiter-api</artifactId>
                <version>${junit.jupiter.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.junit.jupiter</groupId>
                <artifactId>junit-jupiter-engine</artifactId>
                <version>${junit.jupiter.version}</version>
                <scope>test</scope>
            </dependency>
            <!-- Spring Boot Test Starter -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-test</artifactId>
                <!-- Spring Boot starter parent 已经管理了它的版本 -->
            </dependency>
        </dependencies>
    </dependencyManagement>


    <build>
        <pluginManagement>
            <plugins>
                <!-- Spring Boot Maven Plugin -->
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <!-- Spring Boot starter parent 已经管理了它的版本 -->
                </plugin>
                <!-- Maven Compiler Plugin: 指定JDK编译版本 -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.11.0</version> <!-- 较新版本以支持Java 17特性 -->
                    <configuration>
                        <source>${maven.compiler.source}</source>
                        <target>${maven.compiler.target}</target>
                        <encoding>${project.build.sourceEncoding}</encoding>
                    </configuration>
                </plugin>
                <!-- Maven Resources Plugin: 确保资源文件处理 -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-resources-plugin</artifactId>
                    <version>3.3.1</version>
                    <configuration>
                        <encoding>${project.build.sourceEncoding}</encoding>
                    </configuration>
                </plugin>
                <!-- Maven Jar Plugin -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-jar-plugin</artifactId>
                    <version>3.3.0</version>
                </plugin>
                <!-- Skip tests in packaging phase if not needed for packaging -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>3.2.5</version> <!-- JUnit 5 通常需要更新的 surefire 版本 -->
                </plugin>
            </plugins>
        </pluginManagement>

        <!-- 继承插件到子模块 -->
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

</project>
