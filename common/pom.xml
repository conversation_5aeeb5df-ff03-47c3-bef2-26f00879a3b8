<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>life.ljs</groupId>
		<artifactId>bank-policy-comparison-platform</artifactId>
		<version>0.0.1-SNAPSHOT</version>
		<relativePath>../pom.xml</relativePath> <!-- 指明父工程的位置 -->
	</parent>

	<artifactId>common</artifactId>
	<packaging>jar</packaging>
	<name>common</name>

	<dependencies>
		<!-- 引入 Lombok -->
		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<!-- 版本由父工程管理，这里不需要指定 -->
		</dependency>
		<!-- JUnit 5 -->
		<dependency>
			<groupId>org.junit.jupiter</groupId>
			<artifactId>junit-jupiter-api</artifactId>
			<!-- 版本由父工程管理，这里不需要指定 -->
		</dependency>
		<dependency>
			<groupId>org.junit.jupiter</groupId>
			<artifactId>junit-jupiter-engine</artifactId>
			<!-- 版本由父工程管理，这里不需要指定 -->
		</dependency>
		<!-- 引入 Spring Boot Starter Test 用于集成测试 -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope> <!-- 仅在测试时使用 -->
		</dependency>
	</dependencies>


</project>
